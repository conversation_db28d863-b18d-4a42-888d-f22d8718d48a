# 修复版main.py - 解决打包后的导入问题
import sys
import os

# 添加当前目录到Python路径
if hasattr(sys, '_MEIPASS'):
    # PyInstaller打包后的临时目录
    base_path = sys._MEIPASS
else:
    # 开发环境
    base_path = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, base_path)

# 导入所有必要的模块
try:
    from flask import Flask, render_template, request, jsonify, send_file
    import json
    import requests
    import os
    from datetime import datetime, timedelta
    from urllib.parse import quote, quote_plus, urlparse, parse_qs
    import io
    import threading
    import queue
    import time
    from PIL import Image
    import tempfile
    import zipfile
    import pyperclip
    import webbrowser
    import tkinter as tk
    from tkinter import messagebox
    from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
    from cryptography.hazmat.primitives import hashes, padding
    from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
except ImportError as e:
    print(f"导入模块失败: {e}")
    input("按回车键退出...")
    sys.exit(1)

# 设置模板路径
if hasattr(sys, '_MEIPASS'):
    # PyInstaller打包后的模板路径
    template_folder = os.path.join(sys._MEIPASS, 'templates')
    app = Flask(__name__, template_folder=template_folder)
else:
    # 开发环境
    app = Flask(__name__)

class ImageDownloader:
    """图片下载功能模块"""
    target_path_suffix = r"\导出图\已完成"

    @staticmethod
    def format_size(bytes_size):
        """格式化文件大小"""
        if bytes_size == 0:
            return "0 MB"
        mb_size = bytes_size / (1024 * 1024)
        if mb_size < 1024:
            return f"{mb_size:.2f} MB"
        return f"{mb_size / 1024:.2f} GB"

    @classmethod
    def should_download_file(cls, file_path, file_name):
        """检查文件是否符合下载条件"""
        if cls.target_path_suffix:
            lower_file_path = file_path.lower()
            lower_suffix = cls.target_path_suffix.lower()
            
            if not lower_file_path.endswith(lower_suffix + '\\' + file_name.lower()) and \
               not lower_file_path.endswith(lower_suffix + '/' + file_name.lower()):
                return False
                
        return os.path.splitext(file_name)[1].lower() in ('.png', '.jpg', '.jpeg')

    @classmethod
    def prepare_directory(cls, base_dir):
        """准备下载目录"""
        os.makedirs(base_dir, exist_ok=True)
        return base_dir

class ImageProcessor:
    def __init__(self):
        self.search_base_path = r"E:\图片\原图"
        self.current_output_dir = ""
        self.log_messages = []

    def log(self, message, message_type='info'):
        """记录日志"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        self.log_messages.append({
            'time': timestamp,
            'message': message,
            'type': message_type
        })
        print(f"[{timestamp}] {message}")

    def get_fulfilment_id(self, order):
        """获取fulfilmentProductSkuId"""
        try:
            sku_list = order.get('skuQuantityDetailList', [])
            if not sku_list:
                self.log("⚠️ skuQuantityDetailList为空", 'warning')
                return "无FulfilmentID"
            return str(sku_list[0].get('fulfilmentProductSkuId', '无FulfilmentID'))
        except Exception as e:
            self.log(f"获取fulfilmentID异常: {str(e)}", 'error')
            return "错误ID"

    def download_image(self, url, save_path):
        """下载图片文件"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.example.com/'
        }

        try:
            response = requests.get(url, headers=headers, stream=True, timeout=15)
            response.raise_for_status()

            if response.status_code == 200:
                with open(save_path, 'wb') as f:
                    for chunk in response.iter_content(1024):
                        f.write(chunk)
                return True
            return False
        except Exception as e:
            self.log(f"下载异常: {str(e)}", 'error')
            return False

    def detect_image_extension(self, url):
        """根据URL猜测图片扩展名"""
        if 'jpeg' in url.lower() or 'jpg' in url.lower():
            return '.jpg'
        elif 'png' in url.lower():
            return '.png'
        elif 'gif' in url.lower():
            return '.gif'
        elif 'webp' in url.lower():
            return '.webp'
        return '.jpg'

    def download_api_image(self, url, save_path, max_retries=3, retry_interval=2):
        """下载API图片，带重试机制"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://www.example.com/',
            'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8'
        }
        
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    self.log(f"🔄 第 {attempt + 1} 次重试下载API图片...", 'info')
                    time.sleep(retry_interval)
                
                response = requests.get(url, headers=headers, stream=True, timeout=15)
                response.raise_for_status()
                
                if response.status_code == 200:
                    with open(save_path, 'wb') as f:
                        for chunk in response.iter_content(1024):
                            f.write(chunk)
                    self.log(f"✅ API图片下载成功", 'success')
                    return True
            except Exception as e:
                self.log(f"❌ 第 {attempt + 1} 次下载API图片失败: {str(e)}", 'error')
        
        return False

    def search_local_images(self, product_name, search_path="", strict_search=True):
        """搜索本地图片"""
        cleaned_name = product_name.replace("2D Flat ", "").strip()
        search_query = cleaned_name
        
        if search_path and strict_search:
            if not search_path.endswith('\\'):
                search_path += '\\'
            search_query = f"path:\"{search_path}\" {search_query}"
                
        search_params = {
            "search": search_query,
            "json": 1,
            "path_column": 1,
            "size_column": 1,
            "sort": "name",
            "ascending": 1
        }
        
        search_url = "http://localhost:8080/"
        
        try:
            response = requests.get(search_url, params=search_params, timeout=30)
            response.raise_for_status()
            data = response.json()
            
            valid_files = []
            for item in data.get("results", []):
                file_name = item.get('name', '')
                file_path = f"{item.get('path', '')}\\{file_name}".replace("\\\\", "\\")
                
                if ImageDownloader.should_download_file(file_path, file_name):
                    valid_files.append({
                        'name': file_name,
                        'path': file_path,
                        'size': item.get('size', 0),
                        'url': f"http://127.0.0.1:8080/{quote(file_path)}"
                    })
            
            return valid_files
            
        except Exception as e:
            self.log(f"❌ 搜索失败: {str(e)}", 'error')
            return []

    def get_product_api_image(self, product_id, json_data):
        """获取商品的API图片URL"""
        try:
            raw_orders = json_data.get('result', {}).get('subOrderForSupplierList', [])
            
            for order in raw_orders:
                if not order.get('isFirst'):
                    continue
                    
                sku_list = order.get('skuQuantityDetailList', [])
                if not sku_list:
                    continue
                    
                sku_id = str(sku_list[0].get('fulfilmentProductSkuId', ''))
                if sku_id == product_id:
                    image_url = order.get('productSkcPicture', '')
                    if image_url:
                        return image_url
            
            return None
        except Exception as e:
            self.log(f"❌ 获取API图片URL失败: {str(e)}", 'error')
            return None

    def process_json_data(self, json_str):
        """处理JSON数据"""
        try:
            data = json.loads(json_str)
            raw_orders = data.get('result', {}).get('subOrderForSupplierList', [])
            original_count = len(raw_orders)

            sub_orders = [order for order in raw_orders if order.get('isFirst')]
            first_order_count = len(sub_orders)

            self.log(f"订单统计: 原始订单数={original_count} | 首单数={first_order_count}", 'info')

            if not sub_orders:
                return None, "未找到首单订单数据"

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            folder_name = f"商品数据_{timestamp}_共{first_order_count}组"
            os.makedirs(folder_name, exist_ok=True)
            self.current_output_dir = folder_name

            products = []
            success_count = 0

            for index, order in enumerate(sub_orders, 1):
                try:
                    product_name = order.get('productName', '未知商品').strip()
                    sku_id = self.get_fulfilment_id(order)
                    image_url = order.get('productSkcPicture', '').strip()

                    products.append({
                        'name': product_name,
                        'id': sku_id,
                        'api_image_url': image_url
                    })

                    if image_url and sku_id not in ["无FulfilmentID", "错误ID"]:
                        ext = os.path.splitext(image_url)[1].split('?')[0]
                        if not ext:
                            ext = self.detect_image_extension(image_url)

                        img_path = os.path.join(folder_name, f"{sku_id}{ext}")

                        if self.download_image(image_url, img_path):
                            success_count += 1

                except Exception as e:
                    self.log(f"第{index}条处理失败: {str(e)}", 'error')
                    continue

            # 保存商品列表
            txt_path = os.path.join(folder_name, "商品列表.txt")
            with open(txt_path, 'w', encoding='utf-8') as f:
                for product in products:
                    f.write(f"{product['name']}----{product['id']}\n")

            return products, f"解析完成！成功处理: {success_count}/{first_order_count}"

        except Exception as e:
            return None, f"处理失败: {str(e)}"

def validate_key():
    """验证 key.vdf 文件的有效性"""
    # 获取正确的key.vdf路径
    if hasattr(sys, '_MEIPASS'):
        # PyInstaller打包后，key.vdf应该与exe在同一目录
        exe_dir = os.path.dirname(sys.executable)
        key_file = os.path.join(exe_dir, "key.vdf")
    else:
        # 开发环境
        key_file = "key.vdf"
    
    if not os.path.exists(key_file):
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("错误", f"未找到授权文件 {key_file}")
            root.destroy()
        except:
            print(f"错误: 未找到授权文件 {key_file}")
        sys.exit(1)

    # 密钥生成配置（必须与加密时一致）
    password = os.getenv('KEY_PASSWORD', b'my_super_secret_password')
    salt = os.getenv('KEY_SALT', b'fixed_salt_value')

    kdf = PBKDF2HMAC(
        algorithm=hashes.SHA256(),
        length=32,
        salt=salt,
        iterations=100000,
    )
    key = kdf.derive(password)

    # 读取加密文件
    try:
        with open(key_file, "rb") as f:
            data = f.read()
            iv = data[:16]
            ciphertext = data[16:]

        # 解密数据
        cipher = Cipher(algorithms.AES(key), modes.CBC(iv))
        decryptor = cipher.decryptor()
        padded_data = decryptor.update(ciphertext) + decryptor.finalize()

        # 移除填充
        unpadder = padding.PKCS7(128).unpadder()
        current_time_bytes = unpadder.update(padded_data) + unpadder.finalize()

        # 验证时间有效性
        stored_time = datetime.fromisoformat(current_time_bytes.decode('utf-8'))

        # 有效期验证（30天）
        if datetime.now() - stored_time > timedelta(days=30):
            try:
                root = tk.Tk()
                root.withdraw()
                messagebox.showerror("错误", "软件授权已过期")
                root.destroy()
            except:
                print("错误: 软件授权已过期")
            sys.exit(1)
            
    except Exception as e:
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("错误", f"授权验证失败: {str(e)}")
            root.destroy()
        except:
            print(f"错误: 授权验证失败: {str(e)}")
        sys.exit(1)

# 全局处理器实例
processor = ImageProcessor()

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/api/parse_json', methods=['POST'])
def parse_json():
    """解析JSON数据"""
    try:
        data = request.get_json()
        json_str = data.get('json_data', '')
        
        if not json_str:
            return jsonify({'success': False, 'message': '请提供JSON数据'})
        
        # 清空之前的日志
        processor.log_messages = []
        
        products, message = processor.process_json_data(json_str)
        
        if products is None:
            return jsonify({
                'success': False, 
                'message': message,
                'logs': processor.log_messages
            })
        
        return jsonify({
            'success': True,
            'message': message,
            'products': products,
            'output_dir': processor.current_output_dir,
            'logs': processor.log_messages
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'系统异常: {str(e)}'})

@app.route('/api/search_images', methods=['POST'])
def search_images():
    """搜索本地图片，并对API图片URL进行代理缩放"""
    try:
        data = request.get_json()
        product_name = data.get('product_name', '')
        product_id = data.get('product_id', '')
        search_path = data.get('search_path', processor.search_base_path)
        strict_search = data.get('strict_search', True)
        json_data = data.get('json_data', {})
        
        if not product_name:
            return jsonify({'success': False, 'message': '请提供商品名称'})
        
        # 搜索本地图片 (URL保持不变)
        local_images = processor.search_local_images(product_name, search_path, strict_search)
        
        # 获取API图片URL
        api_image_url_raw = None
        if json_data and product_id:
            api_image_url_raw = processor.get_product_api_image(product_id, json_data)
        
        # 仅对API图片URL进行代理
        api_image_url_proxied = None
        if api_image_url_raw:
            api_image_url_proxied = f"/api/image_proxy?url={quote_plus(api_image_url_raw)}"
            
        return jsonify({
            'success': True,
            'local_images': local_images,
            'api_image_url': api_image_url_proxied,
            'api_image_url_raw': api_image_url_raw,
            'total_count': len(local_images)
        })
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'搜索失败: {str(e)}'})

@app.route('/api/download_selected', methods=['POST'])
def download_selected():
    """下载选中的图片"""
    try:
        data = request.get_json()
        product_id = data.get('product_id', '')
        # 前端现在发送 'type', 'url', 和 'extension'
        image_url = data.get('url', '')
        file_extension = data.get('extension', '.jpg')
        
        if not product_id or not image_url:
            return jsonify({'success': False, 'message': '缺少商品ID或图片URL'})
        
        if not processor.current_output_dir:
            return jsonify({'success': False, 'message': '请先解析JSON数据'})
        
        # 准备下载目录
        base_dir = os.path.join(processor.current_output_dir, "出单图下载")
        target_dir = ImageDownloader.prepare_directory(base_dir)
        new_name = f"{product_id}{file_extension}"
        local_path = os.path.join(target_dir, new_name)
        
        # 在新的UI中，只有本地图片可以被选择和下载
        success = processor.download_image(image_url, local_path)
        
        if success:
            return jsonify({
                'success': True,
                'message': f'图片下载成功: {new_name}',
                'file_path': local_path
            })
        else:
            return jsonify({'success': False, 'message': '图片下载失败'})
        
    except Exception as e:
        return jsonify({'success': False, 'message': f'下载失败: {str(e)}'})


@app.route('/api/image_proxy')
def image_proxy():
    """图片代理服务，直接返回原图"""
    image_url = request.args.get('url')
    if not image_url:
        return "缺少图片URL参数", 400

    try:
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        response = requests.get(image_url, headers=headers, stream=True, timeout=15)
        response.raise_for_status()

        # 直接返回原图，不进行缩放处理
        img_data = response.content
        
        # 检测图片格式
        try:
            img = Image.open(io.BytesIO(img_data))
            img_format = img.format if img.format else 'JPEG'
            mimetype = f'image/{img_format.lower()}'
        except:
            # 如果无法检测格式，默认为JPEG
            mimetype = 'image/jpeg'
        
        return send_file(io.BytesIO(img_data), mimetype=mimetype)

    except Exception as e:
        print(f"图片代理错误: {e}")
        return "处理图片时出错", 500


@app.route('/api/get_logs')
def get_logs():
    """获取日志信息"""
    return jsonify({'logs': processor.log_messages})

@app.route('/api/get_clipboard', methods=['GET'])
def get_clipboard():
    """获取剪切板内容"""
    try:
        clipboard_content = pyperclip.paste()
        return jsonify({
            'success': True,
            'content': clipboard_content
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'message': f'获取剪切板内容失败: {str(e)}'
        })

if __name__ == '__main__':
    try:
        # 验证授权文件
        validate_key()
        
        # 启动Flask应用
        port = 8726
        url = f'http://localhost:{port}'
        
        # 延迟打开浏览器
        def open_browser():
            time.sleep(1.5)  # 等待Flask服务启动
            webbrowser.open(url)
        
        # 在新线程中打开浏览器
        threading.Thread(target=open_browser, daemon=True).start()
        
        print(f"商品数据管理工具启动中...")
        print(f"服务地址: {url}")
        print(f"浏览器将自动打开，如未打开请手动访问上述地址")
        
        app.run(debug=False, host='0.0.0.0', port=port, use_reloader=False)
        
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        input("按回车键退出...")