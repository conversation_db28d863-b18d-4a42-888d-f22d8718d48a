import os
import subprocess
import sys

def main():
    print("=== 商品数据管理工具打包脚本 ===")
    
    # 检查必要文件
    if not os.path.exists('主程序.py'):
        print("❌ 找不到 主程序.py 文件")
        return

    if not os.path.exists('templates'):
        print("❌ 找不到 templates 文件夹")
        return

    # key.vdf 不打包进exe，需要与exe放在同一目录
    if not os.path.exists('key.vdf'):
        print("⚠️ 注意：key.vdf 文件不存在，打包后需要手动放置到exe同目录")
    
    print("✅ 文件检查完成")
    
    # 构建PyInstaller命令
    cmd = [
        'pyinstaller',
        '--onefile',
        '--console',
        '--clean',  # 清理缓存
        '--noconfirm',  # 不询问确认
        '--name=商品数据管理工具_新版',
        '--add-data=templates;templates',
        '--add-data=settings.ini;.',
        '--hidden-import=flask',
        '--hidden-import=werkzeug',
        '--hidden-import=jinja2',
        '--hidden-import=requests',
        '--hidden-import=PIL',
        '--hidden-import=PIL.Image',
        '--hidden-import=pyperclip',
        '--hidden-import=cryptography',
        '--hidden-import=cryptography.hazmat.primitives.kdf.pbkdf2',
        '--hidden-import=cryptography.hazmat.primitives.hashes',
        '--hidden-import=cryptography.hazmat.primitives.padding',
        '--hidden-import=cryptography.hazmat.primitives.ciphers',
        '--hidden-import=cryptography.hazmat.primitives.ciphers.algorithms',
        '--hidden-import=cryptography.hazmat.primitives.ciphers.modes',
        '--hidden-import=tkinter',
        '--hidden-import=tkinter.messagebox',
        '主程序.py'  # 主程序文件
    ]
    
    print("开始打包...")
    print("执行命令:", ' '.join(cmd))
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True, encoding='utf-8')
        print("✅ 打包成功！")
        print("可执行文件位置: dist/商品数据管理工具_新版.exe")

        # 显示文件大小
        exe_path = "dist/商品数据管理工具_新版.exe"
        if os.path.exists(exe_path):
            size_mb = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"文件大小: {size_mb:.2f} MB")

        # 提示用户复制key.vdf文件
        print("\n📋 重要提示：")
        print("1. 请将 key.vdf 文件复制到 dist/ 目录中，与exe文件放在同一目录")
        print("2. 运行程序时，key.vdf 必须与exe文件在同一目录，否则程序无法启动")

        # 自动复制key.vdf文件
        if os.path.exists('key.vdf'):
            import shutil
            try:
                shutil.copy2('key.vdf', 'dist/key.vdf')
                print("✅ 已自动复制 key.vdf 到 dist/ 目录")
            except Exception as e:
                print(f"⚠️ 自动复制 key.vdf 失败: {e}")
                print("请手动复制 key.vdf 文件到 dist/ 目录")
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败")
        print("错误信息:")
        print(e.stderr)
        print("\n标准输出:")
        print(e.stdout)
    except FileNotFoundError:
        print("❌ 找不到 pyinstaller 命令")
        print("请先安装: pip install pyinstaller")

if __name__ == '__main__':
    main()
    input("按回车键退出...")