#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
一键打包脚本
将Python程序打包成exe文件，无cmd窗口
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def 检查环境():
    """检查打包环境"""
    print("🔍 检查打包环境...")
    
    # 检查Python版本
    python_version = sys.version_info
    print(f"Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 未安装PyInstaller，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller安装完成")
    
    # 检查其他依赖
    依赖包 = ["requests", "Pillow", "configparser"]
    缺失包 = []
    
    for 包名 in 依赖包:
        try:
            __import__(包名)
            print(f"✅ {包名} 已安装")
        except ImportError:
            缺失包.append(包名)
            print(f"❌ {包名} 未安装")
    
    if 缺失包:
        print(f"正在安装缺失的依赖包: {', '.join(缺失包)}")
        for 包名 in 缺失包:
            subprocess.run([sys.executable, "-m", "pip", "install", 包名], check=True)
        print("✅ 所有依赖包安装完成")

def 清理旧文件():
    """清理之前的打包文件"""
    print("\n🧹 清理旧的打包文件...")
    
    清理目录 = ["build", "dist", "__pycache__"]
    清理文件 = ["*.spec"]
    
    for 目录 in 清理目录:
        if os.path.exists(目录):
            shutil.rmtree(目录)
            print(f"✅ 已删除目录: {目录}")
    
    # 清理spec文件
    for spec_file in Path(".").glob("*.spec"):
        spec_file.unlink()
        print(f"✅ 已删除文件: {spec_file}")

def 执行打包():
    """执行打包命令"""
    print("\n📦 开始打包...")
    
    主程序文件 = "全托.py"
    配置文件 = "配置.ini"
    
    # 检查主程序文件是否存在
    if not os.path.exists(主程序文件):
        print(f"❌ 找不到主程序文件: {主程序文件}")
        return False
    
    # 构建PyInstaller命令
    打包命令 = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--windowed",                   # 无控制台窗口
        "--name=商品数据提取工具",        # 指定exe文件名
        "--icon=icon.ico",              # 图标文件（如果存在）
        "--clean",                      # 清理临时文件
        "--noconfirm",                  # 不询问覆盖
        主程序文件
    ]
    
    # 如果配置文件存在，添加到打包中
    if os.path.exists(配置文件):
        打包命令.extend(["--add-data", f"{配置文件};."])
        print(f"✅ 将包含配置文件: {配置文件}")
    
    # 如果图标文件不存在，移除图标参数
    if not os.path.exists("icon.ico"):
        打包命令 = [cmd for cmd in 打包命令 if not cmd.startswith("--icon")]
        print("⚠️ 未找到icon.ico文件，将使用默认图标")
    
    print(f"执行命令: {' '.join(打包命令)}")
    
    try:
        # 执行打包
        result = subprocess.run(打包命令, check=True, capture_output=True, text=True, encoding='utf-8')
        print("✅ 打包成功！")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 打包失败: {e}")
        print(f"错误输出: {e.stderr}")
        return False

def 复制配置文件():
    """复制配置文件到输出目录"""
    print("\n📋 处理配置文件...")
    
    配置文件 = "配置.ini"
    输出目录 = "dist"
    
    if os.path.exists(配置文件) and os.path.exists(输出目录):
        目标路径 = os.path.join(输出目录, 配置文件)
        shutil.copy2(配置文件, 目标路径)
        print(f"✅ 已复制配置文件到: {目标路径}")

def 显示结果():
    """显示打包结果"""
    print("\n🎉 打包完成！")
    
    exe文件路径 = os.path.join("dist", "商品数据提取工具.exe")
    if os.path.exists(exe文件路径):
        文件大小 = os.path.getsize(exe文件路径) / (1024 * 1024)  # MB
        print(f"📁 输出文件: {os.path.abspath(exe文件路径)}")
        print(f"📊 文件大小: {文件大小:.2f} MB")
        
        # 检查配置文件
        配置文件路径 = os.path.join("dist", "配置.ini")
        if os.path.exists(配置文件路径):
            print(f"📋 配置文件: {os.path.abspath(配置文件路径)}")
        
        print("\n💡 使用说明:")
        print("1. 将生成的exe文件和配置.ini文件放在同一目录下")
        print("2. 双击exe文件即可运行程序")
        print("3. 可以通过修改配置.ini文件来调整程序设置")
        
    else:
        print("❌ 未找到生成的exe文件")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 商品数据提取工具 - 一键打包脚本")
    print("=" * 60)
    
    try:
        # 检查环境
        检查环境()
        
        # 清理旧文件
        清理旧文件()
        
        # 执行打包
        if 执行打包():
            # 复制配置文件
            复制配置文件()
            
            # 显示结果
            显示结果()
        else:
            print("❌ 打包失败，请检查错误信息")
            return 1
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户取消操作")
        return 1
    except Exception as e:
        print(f"❌ 发生错误: {e}")
        return 1
    
    print("\n✅ 打包脚本执行完成")
    return 0

if __name__ == "__main__":
    exit_code = main()
    
    # 等待用户按键
    input("\n按回车键退出...")
    sys.exit(exit_code)
